import { z } from 'zod';

// Risk status options
export const riskStatuses = ['identified', 'assessed', 'mitigated', 'occurred', 'closed'] as const;

// Schema for project risk items
export const riskItemSchema = z.object({
	risk_id: z.number().optional(),
	project_id: z.string().uuid(),
	title: z.string().min(1, 'Risk title is required'),
	description: z.string().min(1, 'Risk description is required'),
	wbs_library_item_id: z.string().uuid().nullable().optional(),
	status: z.enum(riskStatuses).default('identified'),
	date_identified: z.string().optional(),
	cause: z.string().nullable().optional(),
	effect: z.string().nullable().optional(),
	program_impact: z.string().nullable().optional(),
	probability: z.number().min(0).max(100).default(50),
	potential_impact: z.number().nullable().optional(),
	mitigation_plan: z.string().nullable().optional(),
	date_for_review: z.string().optional(),
	// Risk owner fields - either a user_id or name/email
	risk_owner_user_id: z.string().uuid().nullable().optional(),
	risk_owner_name: z.string().nullable().optional(),
	risk_owner_email: z.string().email('Please enter a valid email').nullable().optional(),
	created_at: z.string().datetime().optional(),
	updated_at: z.string().datetime().optional(),
});

// Schema for risk filtering
export const riskFilterSchema = z.object({
	status: z.enum(['all', ...riskStatuses]).default('all'),
	date_from: z.string().optional(),
	date_to: z.string().optional(),
	wbs_library_item_id: z.string().uuid().optional(),
});

export type RiskItem = z.infer<typeof riskItemSchema>;
export type RiskFilter = z.infer<typeof riskFilterSchema>;
