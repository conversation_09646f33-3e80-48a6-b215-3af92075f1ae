import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { wbsLibraryItemWithIdSchema } from '$lib/schemas/wbs';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { redirect } from 'sveltekit-flash-message/server';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	// Check authentication
	if (!locals.user) {
		return redirect('/auth/signin', { type: 'error', message: 'Sign in to view.' }, cookies);
	}

	const { supabase } = locals;
	const { client_name, wbs_item_id } = params;

	// Fetch the client
	const { data: client, error: clientError } = await supabase
		.from('client')
		.select('*')
		.eq('name', client_name)
		.limit(1)
		.maybeSingle();

	if (clientError || !client) {
		console.error('Error fetching client:', clientError);
		throw error(404, { message: 'Client not found' });
	}

	// Is user a client admin?
	const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	if (isAdminError) {
		console.error('Error checking client admin status:', isAdminError);
		throw error(500, { message: 'Error checking permissions' });
	}

	// Fetch all WBS libraries
	const { data: wbsLibraries, error: wbsLibrariesError } = await supabase
		.from('wbs_library')
		.select('*')
		.order('name');

	if (wbsLibrariesError) {
		console.error('Error fetching WBS libraries:', wbsLibrariesError);
		throw error(500, { message: 'Error loading WBS libraries' });
	}

	// Fetch the specific WBS item
	const { data: wbsItem, error: wbsItemError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('wbs_library_item_id', wbs_item_id)
		.eq('item_type', 'Custom')
		.limit(1)
		.maybeSingle();

	if (wbsItemError || !wbsItem) {
		console.error('Error fetching WBS item:', wbsItemError);
		throw error(404, { message: 'WBS item not found' });
	}

	// Verify the item belongs to this client
	if (wbsItem.client_id !== client.client_id) {
		throw error(403, { message: 'Not authorized to view this WBS item' });
	}

	// Create form with schema validation and prefill with current data
	const form = await superValidate(wbsItem, zod(wbsLibraryItemWithIdSchema));

	return {
		client,
		wbsLibraries,
		wbsItem,
		isAdmin,
		form,
	};
};

export const actions: Actions = {
	updateItem: async ({ request, locals, params }) => {
		const { supabase } = locals;
		const { client_name, wbs_item_id } = params;

		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemWithIdSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Ensure wbs_item_id in params matches the form data
		if (form.data.wbs_library_item_id !== wbs_item_id) {
			return fail(400, { form, error: 'Invalid WBS item ID' });
		}

		// Fetch the client
		const { data: client, error: clientError } = await supabase
			.from('client')
			.select('*')
			.eq('name', client_name)
			.limit(1)
			.maybeSingle();

		if (clientError || !client) {
			console.error('Error fetching client:', clientError);
			return fail(404, { form, error: 'Client not found' });
		}

		// Ensure client_id in form matches the current client
		if (form.data.client_id !== client.client_id) {
			return fail(400, { form, error: 'Invalid client ID' });
		}

		// Update the WBS item
		const { error: updateError } = await supabase
			.from('wbs_library_item')
			.update({
				code: form.data.code,
				description: form.data.description,
				scope: form.data.cost_scope,
				parent_item_id: form.data.parent_item_id,
				level: form.data.level,
			})
			.eq('wbs_library_item_id', form.data.wbs_library_item_id)
			.eq('item_type', 'Custom');

		if (updateError) {
			console.error('Error updating WBS item:', updateError);
			return fail(500, { form, error: 'Failed to update WBS item' });
		}

		return message(form, { type: 'success', text: 'WBS item updated successfully' });
	},

	deleteItem: async ({ locals, params, cookies }) => {
		const { supabase } = locals;
		const { wbs_item_id } = params;

		// Delete the WBS item
		const { error: deleteError } = await supabase
			.from('wbs_library_item')
			.delete()
			.eq('wbs_library_item_id', wbs_item_id)
			.eq('item_type', 'Custom');

		if (deleteError) {
			console.error('Error deleting WBS item:', deleteError);
			return fail(500, { error: 'Failed to delete WBS item' });
		}

		// Redirect back to the WBS list after deletion
		redirect(`../`, { type: 'success', message: 'WBS item deleted successfully' }, cookies);
	},
};
