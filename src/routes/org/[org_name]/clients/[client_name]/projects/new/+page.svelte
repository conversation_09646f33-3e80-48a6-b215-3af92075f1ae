<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { superForm } from 'sveltekit-superforms';
	import type { PageData } from './$types';
	import { toast } from 'svelte-sonner';
	import { StandardRibaStages } from '$lib/project_utils';

	const { data }: { data: PageData } = $props();

	const formHandler = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form, enhance } = formHandler;
</script>

<div class="container mx-auto max-w-2xl py-8">
	<h1>Create a Project</h1>

	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Project Name -->
				<Form.Field form={formHandler} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Project Name</Form.Label>
							<Input {...props} bind:value={$form.name} required placeholder="Enter project name" />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- WBS Library Selection -->
				<Form.Field form={formHandler} name="wbs_library_id">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>WBS Library</Form.Label>
							<select
								{...props}
								bind:value={$form.wbs_library_id}
								class="border-input bg-background ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden"
							>
								<option selected disabled hidden value={undefined}>Select a WBS library</option>
								{#each data.wbsLibraries as library (library.wbs_library_id)}
									<option value={library.wbs_library_id}>{library.name}</option>
								{/each}
							</select>
						{/snippet}
					</Form.Control>
					<Form.Description
						>Select a Work Breakdown Structure library for this project</Form.Description
					>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Description -->
				<Form.Field form={formHandler} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description (optional)</Form.Label>
							<Textarea
								{...props}
								placeholder="Brief description of the project"
								class="resize-none"
								bind:value={$form.description}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Stage Selection -->
				<Form.Field form={formHandler} name="selected_stages">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Project Stages</Form.Label>
							<div class="space-y-3">
								{#each StandardRibaStages as stage (stage.stage_order)}
									<div class="flex items-start space-x-3">
										<Checkbox
											id="stage-{stage.stage_order}"
											checked={$form.selected_stages.includes(stage.stage_order)}
											onCheckedChange={(checked) => {
												if (checked) {
													$form.selected_stages = [...$form.selected_stages, stage.stage_order];
												} else {
													$form.selected_stages = $form.selected_stages.filter(
														(s) => s !== stage.stage_order,
													);
												}
											}}
										/>
										<div class="grid gap-1.5 leading-none">
											<label
												for="stage-{stage.stage_order}"
												class="cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
											>
												Stage {stage.stage_order}: {stage.name}
											</label>
											{#if stage.description}
												<p class="text-muted-foreground text-xs">
													{stage.description.split('\n')[0]}
												</p>
											{/if}
										</div>
									</div>
								{/each}
							</div>
						{/snippet}
					</Form.Control>
					<Form.Description>
						Select which stages should be included in this project. You can customize stages later.
					</Form.Description>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-4">
					<Form.Button class="w-full">Create Project</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
